package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.MobileLayoutBuilder;
import com.facishare.paas.appframework.metadata.layout.MobileLayoutProcessorImpl;

public class MobileLayoutFactory implements ExtraLayoutFactory {

    static {
        ExtraLayoutManage.register(LayoutTypes.MOBILE, new MobileLayoutFactory());
    }

    @Override
    public String getLayoutType() {
        return LayoutTypes.MOBILE;
    }

    @Override
    public LayoutExt getLayoutExt(LayoutProcessorContext context) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPEN_BUSINESS_LAYOUT_GRAY, context.getTenantId())) {
            return new MobileLayoutProcessorImpl(context.getPageType(), context.getWebLayout(),
                    context.getDescribeExt(), context.getObjectData(), context.getComponentConfig())
                    .processLayout();
        }
        return MobileLayoutBuilder
                .builder()
                .webLayout(context.getWebLayout())
                .describeExt(context.getDescribeExt())
                .componentConfig(context.getComponentConfig())
                .pageType(context.getPageType())
                .objectData(context.getObjectData())
                .build()
                .getMobileLayout();
    }
}
