package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.IRenderTypeComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.RelatedListFormComponentExt;
import com.facishare.paas.appframework.metadata.layout.factory.ExtraLayoutManage;
import com.facishare.paas.appframework.metadata.layout.factory.LayoutProcessorContext;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.fxiaoke.functions.utils.Maps;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class EditLayoutRender {
    private FunctionLogicService functionLogicService;
    private ButtonLogicService buttonLogicService;
    private FunctionPrivilegeService functionPrivilegeService;
    private DescribeLogicService describeLogicService;
    private LayoutLogicService layoutLogicService;
    private User user;
    private PageType pageType;
    private LayoutAgentType layoutAgentType;
    private ILayout layout;
    private IObjectDescribe describe;
    private LicenseService licenseService;
    private List<IObjectDescribe> detailDescribes;
    private Map<ButtonUsePageType, List<IButton>> customButtonMap;
    private Map<String, Localization> localizationMap;
    private boolean ignoreFunctionPrivilege;

    private boolean existMultiLanguage;
    private final String appId;


    private List<IComponent> oldWebComponents;

    @Builder
    private EditLayoutRender(FunctionLogicService functionLogicService,
                             ButtonLogicService buttonLogicService,
                             FunctionPrivilegeService functionPrivilegeService,
                             DescribeLogicService describeLogicService,
                             LayoutLogicService layoutLogicService,
                             User user, PageType pageType,
                             LayoutAgentType layoutAgentType,
                             ILayout layout,
                             IObjectDescribe describe,
                             LicenseService licenseService,
                             List<IObjectDescribe> detailDescribes,
                             Map<ButtonUsePageType, List<IButton>> customButtonMap,
                             Map<String, Localization> localizationMap,
                             boolean ignoreFunctionPrivilege,
                             boolean existMultiLanguage,
                             String appId) {
        this.functionLogicService = functionLogicService;
        this.buttonLogicService = buttonLogicService;
        this.functionPrivilegeService = functionPrivilegeService;
        this.describeLogicService = describeLogicService;
        this.layoutLogicService = layoutLogicService;
        this.user = user;
        this.pageType = pageType;
        this.layoutAgentType = layoutAgentType;
        this.layout = layout;
        this.describe = describe;
        this.licenseService = licenseService;
        this.detailDescribes = detailDescribes;
        this.customButtonMap = customButtonMap;
        this.localizationMap = localizationMap;
        this.ignoreFunctionPrivilege = ignoreFunctionPrivilege;
        this.existMultiLanguage = existMultiLanguage;
        this.appId = appId;
    }

    public void render() {
        detailDescribes = CollectionUtils.nullToEmpty(detailDescribes);
        //初始化自定义按钮
        initCustomButtonMap();
        switch (pageType) {
            case Designer:
                renderDesigner();
                break;
            case Add:
            case Edit:
                renderAddOrEdit();
                break;
            default:
                break;
        }
        //重置布局中组件的order
        LayoutComponents.restoreComponentOrder(LayoutExt.of(layout));
    }

    private void renderDesigner() {
        //处理按钮信息
        processButtonInfo();
        //处理组件列表
        processComponents(localizationMap);
        //处理UI事件中的函数名称
        processUIEventFunction();
    }

    private void renderAddOrEdit() {
        //启用了移动端布局的话，使用mobile_layout的components、layout_structure以及hidden_components
        if (isMobileAgent() && LayoutExt.of(layout).isEnableMobileLayout()) {
            oldWebComponents = LayoutExt.of(layout).getComponentsSilently();
            replaceWithMobileLayout();
        }
        //按照功能权限过滤自定义按钮
        filterCustomButton();
        //处理布局的按钮列表
        processLayoutButtons();
        //移除head_info组件
        EditLayout.of(layout).removeHeadInfo();
        //处理组件列表
        processComponents();
        //处理从对象组件
        processDetailObjComponents();
        // 处理相关对象组件
        processRelatedListFormComponents();
        //将form_component放第一位，防止老代码报错
        EditLayout.of(layout).placeFormComponentToFirst();
        //没有启用移动端布局的话，将web端布局适配成移动端布局
        if (isMobileAgent() && !LayoutExt.of(layout).isEnableMobileLayout()) {
            replaceWithMobileLayout();
        }
        //清空mobile_layout，减小接口返回值
        layout.setMobileLayout(null);
    }

    private void processRelatedListFormComponents() {
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<RelatedListFormComponentExt> relatedListFormComponents = layoutExt.getRelatedListFormComponents();
        if (CollectionUtils.empty(relatedListFormComponents)) {
            return;
        }
        // 如果对象,字段禁用删除,需要删除相关对象组件
        List<String> deleteComponents = relatedListFormComponents.stream()
                .filter(it -> Objects.isNull(renderRelatedListForm(it)))
                .map(RelatedListFormComponentExt::getName)
                .collect(Collectors.toList());

        for (RelatedListFormComponentExt relatedListFormComponent : relatedListFormComponents) {
            if (deleteComponents.contains(relatedListFormComponent.getName())) {
                continue;
            }
            IObjectDescribe relatedDescribe = describeLogicService.findObject(user.getTenantId(), relatedListFormComponent.getRefObjectApiName());
            processRelatedListFormComponentButtonInfo(relatedListFormComponent, relatedDescribe);
        }
        if (CollectionUtils.empty(deleteComponents)) {
            return;
        }
        List<IComponent> components = layoutExt.getComponentsSilently();
        components.removeIf(it -> deleteComponents.contains(it.getName()));
        //将不在components中的组件从layout_structure里清理掉
        LayoutStructure.clearLayoutStructureByComponents(layoutExt, components);
        layoutExt.setComponents(components);
    }

    private void processRelatedListFormComponentButtonInfo(RelatedListFormComponentExt relatedListFormComponent, IObjectDescribe relatedDescribe) {
        List<IButton> normalButtons = EditLayout.buildDefaultListNormalDetailObjButtons(relatedDescribe, isMobileAgent());
        normalButtons.removeIf(it -> StringUtils.equals(ObjectAction.IMPORT_EXCEL.getActionCode(), it.getName()));
        normalButtons = relatedListFormComponent.filterAndSortButtons(normalButtons, ListComponentInfo.ButtonRenderType.LIST_NORMAL);
        relatedListFormComponent.set("normalButtons", normalButtons.stream().map(LayoutButtonExt::of).map(LayoutButtonExt::toMap).collect(Collectors.toList()));

        /* 本期只保留从查重关联新建的按钮
        List<IButton> batchButtons = EditLayout.buildDefaultListBatchDetailObjButtons(relatedListFormComponent.getRefObjectApiName());
        batchButtons = relatedListFormComponent.filterAndSortButtons(batchButtons, ListComponentInfo.ButtonRenderType.LIST_BATCH);
        relatedListFormComponent.set("batchButtons", batchButtons.stream().map(LayoutButtonExt::of).map(LayoutButtonExt::toMap).collect(Collectors.toList()));

        List<IButton> singleButtons = EditLayout.buildDefaultListSingleDetailObjButtons(user.getTenantId(), relatedListFormComponent.getRefObjectApiName());
        singleButtons = relatedListFormComponent.filterAndSortButtons(singleButtons, ListComponentInfo.ButtonRenderType.LIST_SINGLE);
        relatedListFormComponent.set("singleButtons", singleButtons.stream().map(LayoutButtonExt::of).map(LayoutButtonExt::toMap).collect(Collectors.toList()));
         */
    }

    private RelatedListFormComponentExt renderRelatedListForm(RelatedListFormComponentExt component) {
        // 编辑页不下发 相关对象组件
        if (pageType == PageType.Edit) {
            return null;
        }
        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            log.warn("RelatedListForm object:{} is slave,ei:{}", describe.getApiName(), user.getTenantId());
            return null;
        }
        String refObjectApiName = component.getRefObjectApiName();
        String fieldApiName = component.getFieldApiName();
        IObjectDescribe objectDescribe;
        try {
            objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), refObjectApiName);
        } catch (ObjectDefNotFoundError e) {
            log.warn("RelatedListForm objNotFound, ei:{},refApiName:{},fieldApiName:{}", user.getTenantId(), refObjectApiName, fieldApiName);
            return null;
        }
        if (!hasFunctionPrivilege(user, refObjectApiName, fieldApiName)) {
            log.warn("RelatedListForm funPrivilege, ei:{},refApiName:{},fieldApiName:{}", user.getTenantId(), refObjectApiName, fieldApiName);
            return null;
        }
        if (!describeLogicService.isRelatedListFormSupportObject(user, describe.getApiName(), objectDescribe, fieldApiName)) {
            log.warn("RelatedListForm unSupport, component:{} unSupport,ei:{},apiName:{},refApiName:{}", component.getComponentName(), user.getTenantId(), describe.getApiName(), refObjectApiName);
            return null;
        }

        // 从字段描述同步组件名称
        component.syncHeaderFromDescribe(objectDescribe, fieldApiName);
        // 不是移动端组件,只校验组件是否需要移除
        if (!isMobileAgent()) {
            return component;
        }

        // 移动端组件需要重新渲染组件
        List<ITableColumn> includeFields = getIncludeFields(objectDescribe, component);
        ITableComponent tableComponent = new TableComponent();
        tableComponent.setIncludeFields(includeFields);
        TableComponentRender.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .user(user)
                .skipCorrectLabel(true)
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .tableComponentExt(TableComponentExt.of(tableComponent))
                .build()
                .render();
        component.setIncludeFields(tableComponent.getIncludeFields());
        component.setRenderType(IRenderTypeComponentInfo.RENDER_TYPE_CARD);
        return component;
    }

    /**
     * 校验有没有功能权限
     *
     * @param user
     * @param refObjectApiName 对象ApiName
     * @param fieldApiName     字段 apiName
     * @return true 有功能权限 false 没有功能权限
     */
    private boolean hasFunctionPrivilege(User user, String refObjectApiName, String fieldApiName) {
        //如果设置了不校验功能权限,则直接返回true
        if (ignoreFunctionPrivilege) {
            return true;
        }
        boolean funPrivilegeCheck = functionPrivilegeService.funPrivilegeCheck(user, refObjectApiName, ObjectAction.CREATE.getActionCode());
        if (!funPrivilegeCheck) {
            return false;
        }
        Set<String> readonlyFields = functionPrivilegeService.getReadonlyFields(user, refObjectApiName);
        return !readonlyFields.contains(fieldApiName);
    }

    private List<ITableColumn> getIncludeFields(IObjectDescribe objectDescribe, RelatedListFormComponentExt component) {
        List<ITableColumn> includeFields = component.getIncludeFields();
        if (CollectionUtils.notEmpty(includeFields)) {
            return includeFields;
        }
        List<ILayout> mobileListLayout = layoutLogicService.findMobileListLayout(buildLayoutContext(), objectDescribe, false);
        return CollectionUtils.nullToEmpty(mobileListLayout).stream().findFirst()
                .map(LayoutExt::of)
                .flatMap(LayoutExt::getTableComponent)
                .map(it -> getIncludeFields(objectDescribe, it))
                .orElse(Lists.newArrayList());
    }

    private LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(user, appId);
    }

    private List<ITableColumn> getIncludeFields(IObjectDescribe objectDescribe, TableComponent tableComponent) {
        render(user, objectDescribe, tableComponent);
        List<IFieldSection> fieldSections = tableComponent.getFieldSections();
        if (CollectionUtils.empty(fieldSections)) {
            return tableComponent.getIncludeFields();
        }
        return fieldSections.get(0).getFields().stream()
                .map(x -> ((DocumentBasedBean) x).getContainerDocument())
                .map(TableColumn::new)
                .collect(Collectors.toList());
    }

    private void render(User user, IObjectDescribe objectDescribe, ITableComponent tableComponent) {
        TableComponentRender.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .user(user)
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .tableComponentExt(TableComponentExt.of(tableComponent))
                .build()
                .render();
    }

    private void processDetailObjComponents() {
        if (isMobileAgent()) {
            LayoutExt.of(layout).getMasterDetailComponents().forEach(component -> {
                List<Map<String, Object>> includesFields = component.get(ITableComponent.INCLUDE_FIELDS, List.class);
                if (CollectionUtils.empty(includesFields)) {
                    return;
                }
                IObjectDescribe detailDescribe = detailDescribes.stream()
                        .filter(x -> x.getApiName().equals(component.get(IMultiTableComponent.REF_OBJECT_API_NAME)))
                        .findFirst()
                        .get();
                ITableComponent tableComponent = new TableComponent();
                tableComponent.set(ITableComponent.INCLUDE_FIELDS, includesFields);
                TableComponentRender.builder()
                        .functionPrivilegeService(functionPrivilegeService)
                        .user(user)
                        .skipCorrectLabel(true)
                        .describeExt(ObjectDescribeExt.of(detailDescribe))
                        .tableComponentExt(TableComponentExt.of(tableComponent))
                        .build()
                        .render();
                component.set(ITableComponent.INCLUDE_FIELDS, includesFields);
            });
        }
    }

    private LayoutExt getMobileLayout() {
        return ExtraLayoutManage.getLayoutFactory(LayoutTypes.MOBILE)
                .getLayoutExt(LayoutProcessorContext
                        .builder()
                        .pageType(pageType)
                        .webLayout(LayoutExt.of(layout))
                        .describeExt(ObjectDescribeExt.of(describe))
                        .build());
    }

    private void replaceWithMobileLayout() {
        LayoutExt mobileLayout = getMobileLayout();

        layout.setButtons(mobileLayout.getButtons());
        layout.setHiddenButtons(mobileLayout.getHiddenButtons());
        layout.setComponents(mobileLayout.getComponentsSilently());
        layout.setHiddenComponents(mobileLayout.getHiddenComponents());
        layout.setLayoutStructure(mobileLayout.getLayoutStructure());
    }

    private void processComponents() {
        processComponents(null);
    }

    private void processComponents(Map<String, Localization> localizationMap) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<IComponent> components = layoutExt.getComponentsSilently();

        if (pageType == PageType.Designer) {
            //先计算一下组件的order
            LayoutComponents.restoreComponentOrder(layoutExt);
            //按照布局里配置的显示顺序来对从对象进行排序，先将从对象按照id排序，防止没有在布局管理配置过顺序的从对象出现乱序。
            Collections.sort(detailDescribes, Comparator.comparing(IObjectDescribe::getId));
            Collections.sort(detailDescribes, Comparator.comparingInt(o -> layoutExt.getDetailComponentOrder(o.getApiName())));

            // 同步相关对象组件名称
            Set<String> refObjectApiNames = layoutExt.getRelatedListFormComponents().stream().map(x -> x.getRefObjectApiName())
                    .collect(Collectors.toSet());
            Map<String, IObjectDescribe> refDescribeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), refObjectApiNames);
            for (RelatedListFormComponentExt listFormComponentExt : layoutExt.getRelatedListFormComponents()) {
                String refObjectApiName = listFormComponentExt.getRefObjectApiName();
                String fieldApiName = listFormComponentExt.getFieldApiName();
                IObjectDescribe objectDescribe = refDescribeMap.get(refObjectApiName);
                //相关对象的描述被删除的，把相关对象组件删掉
                if (Objects.isNull(objectDescribe)) {
                    components.removeIf(x -> listFormComponentExt.getName().equals(x.getName()));
                } else {
                    listFormComponentExt.syncHeaderFromDescribe(objectDescribe, fieldApiName);
                }
            }
            if (layoutExt.isEditLayout()) {
                layoutExt.getFormComponent().ifPresent(FormComponentExt::removeHideComponent);
            }
        } else {
            //新建编辑页清理掉没有字段的自定义表单组件
            components.removeIf(x -> !ComponentExt.FORM_COMPONENT.equals(x.getName())
                    && ComponentExt.of(x).isFormType()
                    && FormComponentExt.of((IFormComponent) x).hasNoField());
        }


        List<String> oldDetailObjComponentNames = layoutExt.getMasterDetailComponentNames();
        List<IComponent> detailObjComponents = Lists.newArrayList();
        List<IComponent> newDetailObjComponents = Lists.newArrayList();
        detailDescribes.forEach(detailDescribe -> {
            IComponent detailObjComponent = LayoutComponents.buildDetailObjectComponent(detailDescribe);
            if (!layoutExt.isComponentHidden(detailObjComponent.getName())) {
                ComponentExt.of(detailObjComponent).syncProps(components);
                ComponentExt.of(detailObjComponent).mergeDetailObjButtons(detailDescribe, isMobileAgent());
                processDetailRenderType(detailObjComponent);
                detailObjComponents.add(detailObjComponent);

                if (!oldDetailObjComponentNames.contains(detailObjComponent.getName())) {
                    newDetailObjComponents.add(detailObjComponent);
                }
            }
        });
        components.removeIf(x -> oldDetailObjComponentNames.contains(x.getName()));
        components.addAll(detailObjComponents);
        if (CollectionUtils.notEmpty(newDetailObjComponents)) {
            //构造页签容器
            if (!isMobileAgent() && components.stream().noneMatch(x -> ComponentExt.of(x).isTabs())) {
                TabsComponent tabsComponent = buildTabsComponent(newDetailObjComponents);
                components.add(tabsComponent);
                WebDetailLayout.of(layout).addComponents(Lists.newArrayList(tabsComponent));
            } else {
                //将新增的从对象放入layout_structure或页签导航中
                LayoutStructure.modifyLayoutStructure(layoutExt, components, false);
            }
        }
        //将不在components中的组件从layout_structure里清理掉
        LayoutStructure.clearLayoutStructureByComponents(layoutExt, components);

        ComponentHeaderSetter.builder()
                .tenantId(layoutExt.getTenantId())
                .layoutType(layoutExt.isFlowLayout() ? LayoutTypes.DETAIL : LayoutTypes.EDIT)
                .layoutVersion(LayoutVersion.V3)
                .components(components)
                .sourceType(ComponentHeaderSetter.SOURCE_TYPE_DESIGNER)
                .objectApiName(layoutExt.getRefObjectApiName())
                .layoutApiName(layoutExt.getName())
                .existMultiLanguage(existMultiLanguage)
                .componentPreKeyMap(Optional.ofNullable(layoutLogicService).map(x -> x.findComponentPreKeys(layoutExt.getComponentsSilently())).orElse(Maps.newHashMap()))
                .localizationMap(localizationMap)
                .build()
                .reset();

        layoutExt.setComponents(components);
    }

    private void processDetailRenderType(IComponent detailObjComponent) {
        if (!isMobileAgent()) {
            return;
        }
        EditLayout.processDetailRenderType(detailObjComponent, user.getTenantId(), describe.getApiName());
    }

    private TabsComponent buildTabsComponent(List<IComponent> detailObjComponents) {
        TabsComponent tabsComponent = LayoutComponents.buildTabsComponent();
        List<List<String>> childComponents = Lists.newArrayList();
        List<TabSection> tabSections = Lists.newArrayList();
        detailObjComponents.forEach(x -> {
            childComponents.add(Lists.newArrayList(x.getName()));
            TabSection tabSection = new TabSection();
            tabSection.setApiName("tab_" + x.getName());
            tabSection.setHeader(x.getHeader());
            TabSectionExt.of(tabSection).setNameI18nKey(ComponentExt.of(x).getNameI18nKey());
            tabSections.add(tabSection);
        });
        tabsComponent.setComponents(childComponents);
        tabsComponent.setTabs(tabSections);
        return tabsComponent;
    }

    private void processButtonInfo() {
        if (LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        EditLayout.of(layout).mergeDesignerLayoutButton(customButtonMap);
    }

    private void processLayoutButtons() {
        if (LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        EditLayout editLayout = EditLayout.of(layout);
        List<ListComponentInfo> buttonInfo = editLayout.getButtonInfo();
        List<IButton> buttons;
        ListComponentInfo pageButtonInfo;
        if (PageType.Add == pageType) {
            buttons = editLayout.getButtonsAdd(customButtonMap);
            pageButtonInfo = buttonInfo.stream().filter(ListComponentInfo::useForAddPage).findFirst().orElse(null);
        } else {
            buttons = editLayout.getButtonsEdit(customButtonMap);
            pageButtonInfo = buttonInfo.stream().filter(ListComponentInfo::useForEditPage).findFirst().orElse(null);
        }
        if (isMobileAgent()) {
            buttons.removeIf(x -> !LayoutButtonExt.of(x).isMobileButton());
        }
        if (pageButtonInfo != null) {
            buttons = pageButtonInfo.filterAndSortButtons(buttons);
        }
        ButtonExt.filterButtonsByBlacklist(buttons, describe.getApiName());
        ButtonExt.filterButtonsByBlacklist(buttons, ObjectDescribeExt.of(describe).isBiObject());
        layout.setButtons(buttons);
    }

    private void processUIEventFunction() {
        if (functionLogicService == null) {
            return;
        }
        // 补充UI事件中的函数信息
        List<Map<String, Object>> events = layout.getEvents();
        if (CollectionUtils.notEmpty(events)) {
            List<UIEventExt> eventXs = UIEventExt.ofList(events);
            List<String> funcApiNames = eventXs.stream().map(UIEventExt::getFuncApiName).collect(Collectors.toList());
            List<IUdefFunction> functions = functionLogicService.findFunctionByApiNames(user, funcApiNames, describe.getApiName());
            for (UIEventExt e : eventXs) {
                functions.stream().filter(
                        f -> f.getApiName().equals(e.getFuncApiName())).findFirst().ifPresent(func -> {
                    e.set(UIEventExt.FUNC_NAME, func.getFunctionName());
                    e.set(UIEventExt.FUNC_DESCRIBE, func.getRemark());
                });
            }
        }
        layout.setEvents(events);
    }

    private void initCustomButtonMap() {
        if (customButtonMap != null || buttonLogicService == null) {
            return;
        }
        customButtonMap = buttonLogicService.findButtonsByUsePages(user, describe, Lists.newArrayList(ButtonUsePageType.Create, ButtonUsePageType.Edit));
    }

    private void filterCustomButton() {
        if (buttonLogicService == null) {
            return;
        }
        List<IButton> customButtons = Lists.newArrayList();
        customButtonMap.forEach((k, v) -> customButtons.addAll(v));
        List<IButton> validCustomButtons = buttonLogicService.filterByFunPrivilege(user, describe, customButtons);
        Set<String> validButtonNames = validCustomButtons.stream().map(IButton::getName).collect(Collectors.toSet());
        customButtonMap.forEach((k, v) -> v.removeIf(button -> !validButtonNames.contains(button.getName())));
    }


    private boolean isMobileAgent() {
        return LayoutAgentType.MOBILE == layoutAgentType;
    }
}
