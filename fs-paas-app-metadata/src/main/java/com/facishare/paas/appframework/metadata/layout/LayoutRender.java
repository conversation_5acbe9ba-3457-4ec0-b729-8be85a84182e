package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.log.AuditLogConfig;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlignService;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.layout.factory.ExtraLayoutManage;
import com.facishare.paas.appframework.metadata.layout.factory.LayoutProcessorContext;
import com.facishare.paas.appframework.metadata.util.VelocityUtil;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.*;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.SEND_AS_TEMPLATE;
import static com.facishare.paas.appframework.common.util.ObjectAction.SEND_ATTACHMENT;
import static com.facishare.paas.appframework.metadata.layout.component.IComponentInfo.RENDER_TYPE;
import static com.fxiaoke.functions.utils.Maps.newHashMap;

/**
 * Created by zhouwr on 2019/6/24
 */
@Slf4j
@Builder
public class LayoutRender {

    private FunctionPrivilegeService functionPrivilegeService;

    private ButtonLogicService buttonLogicService;

    private RecordTypeLogicService recordTypeLogicService;

    private GdprService gdprService;

    private GlobalFieldAlignService fieldAlignService;

    private LayoutLogicService layoutLogicService;

    private ChangeOrderLogicService changeOrderLogicService;

    private User user;

    private LayoutExt layoutExt;

    private ObjectDescribeExt describeExt;

    private IObjectData objectData;

    private List<RelatedObjectDescribeStructure> relatedObjectList;

    private List<RelatedObjectDescribeStructure> detailObjectList;

    private Map<String, ILayout> listLayoutMap;

    private Map<String, List<IRecordTypeOption>> recordTypeOptionMap;

    private List<IButton> customButtons;

    private List<IComponent> componentConfig;

    private String version;

    private PageType pageType;

    private List<ICustomComponent> newCustomComponents;

    private LicenseService licenseService;

    private boolean fromRecycleBin;

    private boolean excludeButton;

    public void render() {
        switch (pageType) {
            case Related:
                renderRelated();
                break;
            case Detail:
                renderDetail();
                break;
            case NewDetail:
                renderNewDetail();
                break;
            case WebDetail:
                renderWebDetail();
                break;
            default:
                break;
        }
        //清空mobile_layout，减小接口返回值
        layoutExt.setMobileLayout(null);
    }

    private void renderRelated() {
        GroupComponent relatedObjectComponent = buildRelatedComponent();
        List<IComponent> components = GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently();

        //根据配置过滤组件
        filterComponents(components);

        relatedObjectComponent.setChildComponents(ComponentOrder.order(components, layoutExt));
        layoutExt.setComponents(Lists.newArrayList(relatedObjectComponent));
    }

    private void renderDetail() {
        LayoutStructure.restoreLayout(layoutExt, PageType.Detail);
        GroupComponent relatedObjectComponent = buildRelatedComponent();
        List<GroupComponent> detailObjectComponents = buildMasterDetailComponents();
        handleRecordTypeMatchRelation(detailObjectComponents);
        GroupComponent detailComponent = getDetailGroupComponent();
        SimpleComponent simpleComponent = getSimpleComponent();
        //销售记录，修改记录layout在otherComponent中
        GroupComponent otherComponent = getLogGroupComponent();
        List<IButton> detailButtons = getDetailPageButtons();

        modifyLayoutByVersion(relatedObjectComponent, detailComponent, simpleComponent, detailButtons);

        //生成最终的ComponentList
        List<IComponent> finalGroupComponentList = orderComponents(detailComponent, detailObjectComponents,
                relatedObjectComponent, otherComponent, simpleComponent);

        //根据配置过滤组件
        filterComponents(finalGroupComponentList);

        //为layout设置groupComponent
        layoutExt.setComponents(finalGroupComponentList);
        // 旧详情页的按钮还是放在 layout.buttons中
        layoutExt.setButtons(detailButtons);
        layoutExt.removeHeadInfoComponent();
    }

    private void filterComponents(List<IComponent> components) {
        //根据配置过滤组件
        components.removeIf(x -> DefObjConstants.isComponentInvisible(describeExt.getApiName(), x.getName()));
        boolean isMobile = LayoutContext.isMobileLayout() && layoutExt.isEnableMobileLayout();
        List<String> componentNames = layoutLogicService.filterComponentsByFunctionCode(user, describeExt, pageType,
                isMobile, components);
        components.removeIf(it -> componentNames.contains(it.getName()));
    }

    private void renderNewDetail() {
        //老布局默认下发摘要卡片
        if (!layoutExt.isNewLayout()) {
            layoutExt.addComponent(LayoutComponents.buildSummaryCardComponent());
        }
        LayoutStructure.restoreLayout(layoutExt, PageType.NewDetail);
        GroupComponent relatedObjectComponent = buildRelatedComponent();
        List<IMultiTableComponent> detailObjectComponents = buildNewMasterDetailComponents();
        IFormComponent detailComponent = getDetailFormComponent();
        SimpleComponent simpleComponent = getSimpleComponent();
        List<IComponent> groupFieldComponents = getGroupFieldComponents(simpleComponent);
        simpleComponent.setButtons(Lists.newArrayList());
        IComponent logComponent = getLogComponent();
        List<IButton> detailButtons = getDetailPageButtons();

        modifyLayoutByVersion(relatedObjectComponent, detailComponent, simpleComponent, detailButtons);

        //生成最终的ComponentList
        List<IComponent> finalGroupComponentList = orderComponents(detailComponent, detailObjectComponents,
                relatedObjectComponent, logComponent, simpleComponent, groupFieldComponents);

        //个人导航配置的第一个component设为默认展示
        if (CollectionUtils.notEmpty(componentConfig)) {
            layoutExt.setDefaultComponent(componentConfig.get(0).getName());
        }

        //为layout设置groupComponent
        layoutExt.setComponents(finalGroupComponentList);
        // 对于NewDetail接口，不管是老版本的布局还是新版本的布局，最终按钮都要直接放到layout的最外层的buttons中。
        layoutExt.setButtons(detailButtons);
        layoutExt.removeHeadInfoComponent();
    }

    private void renderWebDetail() {
        //启用了移动端布局的话，使用mobile_layout的components、layout_structure以及hidden_components
        boolean useMobileLayout = LayoutContext.isMobileLayout() && layoutExt.isEnableMobileLayout();
        if (useMobileLayout) {
            replaceWithMobileLayout();
        } else if (LayoutAgentType.SIDEBAR == LayoutContext.get().getLayoutAgentType() && layoutExt.isEnableSidebarLayout()) {
            replaceWithSidebarLayout();
        }
        // bug修复：先拷贝一份，避免后续操作（判断isTileRelatedList）需要使用是否存在相关对象来移除组件的判断
        List<RelatedObjectDescribeStructure> relatedObjectListCopy = Lists.newArrayList(relatedObjectList);
        renderSuspendedComponents(useMobileLayout);
        GroupComponent relatedObjectComponent = buildRelatedComponent();
        List<IMultiTableComponent> detailObjectComponents = buildNewMasterDetailComponents();
        //解决从编辑页复制的详情页脏数据问题（会在从对象组件中加如render_type:card参数，对于web端详情页来说，会导致从对象组件渲染异常，所以要删除）
        if (!LayoutContext.isMobileLayout()) {
            detailObjectComponents.forEach(x -> ComponentExt.of(x).toMap().remove(RENDER_TYPE));
        }
        // handleRecordTypeMatchRelation(detailObjectComponents);
        IFormComponent formComponent = getDetailFormComponent();
        SimpleComponent simpleComponent = getSimpleComponent();
        //修改记录
        IComponent logComponent = getLogComponent();

        // 按钮
        List<IButton> detailButtons = getDetailPageButtons();
        modifyLayoutByVersion(relatedObjectComponent, formComponent, simpleComponent, detailButtons);
        layoutExt.setButtonOrder(detailButtons);

        layoutExt.setTopInfo(simpleComponent);
        //生成最终的ComponentList
        List<IComponent> finalGroupComponentList = orderComponentsForWebDetail(formComponent, detailObjectComponents,
                relatedObjectComponent, logComponent, relatedObjectListCopy);
        //移动端设置了个人导航顺序的第一个组件作为default_component
        if (CollectionUtils.notEmpty(componentConfig)) {
            layoutExt.setDefaultComponent(componentConfig.get(0).getName());
        }

        List<Map> leftTabsComponentApiNames = null;
        List<Map> leftBottomComponentApiNames = null;
        //V1的layout需要生成默认的left
        if (!layoutExt.isNewLayout()) {
            List<String> leftTabsApiNames = Lists.newArrayList(ComponentExt.DETAIL_INFO_COMPONENT_NAME, simpleComponent.getName());
            if (!layoutExt.isComponentHidden(ComponentExt.OPERATION_LOG_COMPONENT_NAME)) {
                leftTabsApiNames.add(ComponentExt.OPERATION_LOG_COMPONENT_NAME);
            }
            List<String> leftBottomApiNames = handleNewLayoutSpecialRelatedComponent(finalGroupComponentList);
            List<String> relatedApiNames = GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently().stream().map(IComponent::getName).collect(Collectors.toList());
            List<String> detailApiNames = detailObjectComponents.stream().map(IComponent::getName).collect(Collectors.toList());
            leftTabsComponentApiNames = LayoutComponents.getLeftTabsComponentApiNames(relatedApiNames, detailApiNames,
                    finalGroupComponentList, leftTabsApiNames, layoutExt.getDefaultComponent(), leftBottomApiNames);
            handleNewLayoutSpecialFrameComponent(leftTabsComponentApiNames, finalGroupComponentList);
            leftBottomComponentApiNames = leftBottomApiNames.stream().map(LayoutStructure::convertToMap).collect(Collectors.toList());
        }

        //根据配置过滤组件
        filterComponents(finalGroupComponentList);
        removeUnsupportedComponentsIfOuter(finalGroupComponentList);

        LayoutStructure.buildLayoutStructure(layoutExt, describeExt, finalGroupComponentList, leftTabsComponentApiNames,
                leftBottomComponentApiNames, true, LayoutContext.isMobileLayout() || LayoutContext.get().getLayoutAgentType() == LayoutAgentType.SIDEBAR);

        //处理嵌入页面的URL
        layoutExt.getFrameComponent().forEach(x -> {
            String url = VelocityUtil.replacePlaceholder(x.getUrl(), ObjectDataExt.toMap(objectData));
            x.setUrl(url);
        });

        // web 补充三流组件
        if (!LayoutContext.isMobileLayout()) {
            WebDetailLayout.of(layoutExt).processWebFlowComponents(user.getTenantId(), describeExt);
        }

        // 处理 fieldAlign
        fieldAlignService.handleLayoutWithGlobalFieldAlign(user.getTenantId(), describeExt, layoutExt, RequestContextManager.getContext().getLang());
        layoutLogicService.handleSummaryKeyComponents(user.getTenantId(), describeExt, layoutExt);

        //没有启用移动端布局的话，将web端布局适配成移动端布局
        if (LayoutContext.isMobileLayout() && !layoutExt.isEnableMobileLayout()) {
            replaceWithMobileLayout();
        }
        if (LayoutContext.isSidebarLayout() && !layoutExt.isEnableMobileLayout()) {
            replaceWithSidebarLayout();
        }
        if (describeExt.isBigObject()) {
            Optional.ofNullable(layoutExt.getLayoutStructure()).map(x -> x.put(LayoutStructure.SHOW_TAG, false));
        }
    }

    private void renderSuspendedComponents(boolean useMobileLayout) {
        if (!useMobileLayout) {
            return;
        }
        SuspendedComponentRender.builder()
                .user(user)
                .objectData(objectData)
                .suspendedComponentInfos(layoutExt.getSuspendedComponent())
                .relatedObjectList(relatedObjectList)
                .recordTypeLogicService(recordTypeLogicService)
                .build()
                .render();
    }

    /**
     * 下游企业不展示不支持的组件类型
     *
     * @param components
     */
    private void removeUnsupportedComponentsIfOuter(List<IComponent> components) {
        if (!user.isOutUser()) {
            return;
        }
        if (CollectionUtils.empty(components)) {
            return;
        }
        components.removeIf(this::isUnsupportedComponentForOuterUser);
    }

    private boolean isUnsupportedComponentForOuterUser(IComponent component) {
        // 检查灰度配置的不支持组件类型
        return AppFrameworkConfig.isOuterUserUnsupportedComponentType(user.getTenantId(), component.getType());
    }

    private LayoutExt getMobileLayout() {
        return ExtraLayoutManage.getLayoutFactory(LayoutTypes.MOBILE)
                .getLayoutExt(LayoutProcessorContext
                        .builder()
                        .pageType(PageType.WebDetail)
                        .webLayout(layoutExt)
                        .describeExt(describeExt)
                        .objectData(objectData)
                        .componentConfig(componentConfig)
                        .build());
    }

    private void replaceWithMobileLayout() {
        LayoutExt mobileLayout = getMobileLayout();
        layoutExt.setButtons(mobileLayout.getButtons());
        layoutExt.setButtonStyle(mobileLayout.getButtonStyle());
        layoutExt.setHiddenButtons(mobileLayout.getHiddenButtons());
        layoutExt.buildTabShowMasterField(mobileLayout.getComponentsSilently(), describeExt);
        layoutExt.setComponents(mobileLayout.getComponentsSilently());
        layoutExt.setHiddenComponents(mobileLayout.getHiddenComponents());
        layoutExt.setLayoutStructure(mobileLayout.getLayoutStructure());
        layoutExt.setSuspendedComponent(mobileLayout.getSuspendedComponent());
    }

    private LayoutExt getSidebarLayout() {
        return ExtraLayoutManage.getLayoutFactory(LayoutTypes.SIDEBAR)
                .getLayoutExt(LayoutProcessorContext
                        .builder()
                        .pageType(PageType.WebDetail)
                        .webLayout(layoutExt)
                        .describeExt(describeExt)
                        .objectData(objectData)
                        .componentConfig(componentConfig)
                        .build());
    }

    private void replaceWithSidebarLayout() {
        LayoutExt sidebarLayout = getSidebarLayout();
        layoutExt.setButtons(sidebarLayout.getButtons());
        layoutExt.setButtonStyle(sidebarLayout.getButtonStyle());
        layoutExt.setHiddenButtons(sidebarLayout.getHiddenButtons());
        layoutExt.buildTabShowMasterField(sidebarLayout.getComponentsSilently(), describeExt);
        layoutExt.setComponents(sidebarLayout.getComponentsSilently());
        layoutExt.setHiddenComponents(sidebarLayout.getHiddenComponents());
        layoutExt.setLayoutStructure(sidebarLayout.getLayoutStructure());
    }

    private List<IComponent> getNewComponents(List<IComponent> components) {
        //补充新设计器增加的新组件
        Set<String> apiNames = components.stream().map(x -> x.getName()).collect(Collectors.toSet());
        return layoutExt.getComponentsSilently().stream()
                .filter(x -> !apiNames.contains(x.getName())
                        && !ComponentExt.of(x).isRelatedList()
                        && !ComponentExt.of(x).isMasterDetailComponent()
                        && !ComponentExt.of(x).isTeamComponent()
                        && !ComponentExt.of(x).isSaleLog()
                        && !ComponentExt.of(x).isCustomComponent())
                .collect(Collectors.toList());
    }

    private List<String> handleNewLayoutSpecialRelatedComponent(List<IComponent> components) {
        List<String> layoutSpecialRelatedObjects = AppFrameworkConfig.getLayoutSpecialRelatedObjects(describeExt.getTenantId(), describeExt.getApiName());
        if (CollectionUtils.notEmpty(layoutSpecialRelatedObjects)) {
            return components.stream()
                    .filter(x -> x instanceof RelatedObjectList)
                    .map(x -> (RelatedObjectList) x)
                    .filter(x -> layoutSpecialRelatedObjects.contains(x.getRefObjectApiName()))
                    .map(x -> x.getName())
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private void handleNewLayoutSpecialFrameComponent(List<Map> leftTabsComponentApiNames, List<IComponent> finalGroupComponentList) {
        IFrameComponent frameComponent = AppFrameworkConfig.getLayoutSpecialFrameObjects(describeExt.getTenantId(), describeExt.getApiName());
        if (frameComponent != null) {
            frameComponent.setUrl(frameComponent.getUrl());
            frameComponent.setName("iframe");
            frameComponent.setHeader("Eloqua");
            leftTabsComponentApiNames.add(1, LayoutStructure.convertToMap(frameComponent.getName()));
            finalGroupComponentList.add(frameComponent);
        }
    }

    private List<IComponent> orderComponents(GroupComponent detailComponent, List<GroupComponent> detailObjectComponents,
                                             GroupComponent relatedObjectComponent, GroupComponent otherComponent, SimpleComponent simpleComponent) {
        List<IComponent> components = Lists.newArrayList();
        components.addAll(GroupComponentExt.of(detailComponent).getChildComponentsSilently());
        components.addAll(detailObjectComponents);
        components.addAll(GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently());
        components.addAll(GroupComponentExt.of(otherComponent).getChildComponentsSilently());

        layoutExt.setComponentOrder(components);
        //根据i18nKey重置component的header
        boolean existMultiLanguage = licenseService.isSupportMultiLanguage(user.getTenantId());
        ComponentHeaderSetter.builder()
                .tenantId(user.getTenantId())
                .layoutType(LayoutTypes.DETAIL)
                .layoutVersion(LayoutVersion.V1)
                .components(components)
                .objectApiName(layoutExt.getRefObjectApiName())
                .layoutApiName(layoutExt.getName())
                .existMultiLanguage(existMultiLanguage)
                .componentPreKeyMap(Optional.ofNullable(layoutLogicService).map(x -> x.findComponentPreKeys(layoutExt.getComponentsSilently())).orElse(newHashMap()))
                .build()
                .reset();
        detailObjectComponents = ComponentOrder.order(detailObjectComponents);
        relatedObjectComponent.setChildComponents(ComponentOrder.order(GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently()));

        List<IComponent> finalGroupComponentList = Lists.newArrayList();
        finalGroupComponentList.add(detailComponent);
        finalGroupComponentList.addAll(detailObjectComponents);
        finalGroupComponentList.add(relatedObjectComponent);
        if (!simpleComponent.isHidden()) {
            finalGroupComponentList.add(simpleComponent);
        }
        finalGroupComponentList.add(otherComponent);

        return filterComponentByWhere(finalGroupComponentList, objectData);
    }

    private List<IComponent> orderComponents(IFormComponent detailComponent, List<IMultiTableComponent> detailObjectComponents,
                                             GroupComponent relatedObjectComponent, IComponent logComponent,
                                             SimpleComponent simpleComponent, List<IComponent> topComponents) {
        List<IComponent> finalGroupComponentList = Lists.newArrayList();

        GroupComponent topGroupComponent = new GroupComponent();
        topGroupComponent.setName(ComponentExt.TOP_COMPONENT_NAME);
        topGroupComponent.setHeader(ComponentExt.TOP_COMPONENT_NAME);

        GroupComponent middleGroupComponent = new GroupComponent();
        middleGroupComponent.setName(ComponentExt.MIDDLE_COMPONENT_NAME);
        middleGroupComponent.setHeader(ComponentExt.MIDDLE_COMPONENT_NAME);

        //摘要字段支持隐藏
        if (!simpleComponent.isHidden()) {
            topGroupComponent.addComponent(simpleComponent);
        }
        //支持摘要卡片组件
        layoutExt.getSummaryCardComponent().ifPresent(x -> topGroupComponent.addComponent(x));

        //将相关团队从相关列表转移到顶部列表
        GroupComponentExt.of(relatedObjectComponent).getChildComponentByName(ComponentExt.TEAM_COMPONENT_NAME)
                .filter(x -> !layoutExt.isComponentHidden(x.getName())).ifPresent(x -> {
                    topGroupComponent.addComponent(x);
                    GroupComponentExt.of(relatedObjectComponent).removeChildComponentByName(x.getName());
                });
        topComponents.forEach(x -> topGroupComponent.addComponent(x));

        List<IComponent> components = Lists.newArrayList();
        components.add(detailComponent);
        components.addAll(detailObjectComponents);
        components.addAll(GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently());
        components.add(logComponent);
        //700之后增加的新组件
        components.addAll(getNewComponents(components));

        //根据配置过滤组件
        filterComponents(components);

        //移除布局中设置隐藏的component
        layoutExt.removeHiddenComponents(components);
        //根据i18nKey重置component的header

        boolean existMultiLanguage = licenseService.isSupportMultiLanguage(user.getTenantId());
        ComponentHeaderSetter.builder()
                .tenantId(user.getTenantId())
                .layoutType(LayoutTypes.DETAIL)
                .layoutVersion(LayoutVersion.V1)
                .components(components)
                .objectApiName(layoutExt.getRefObjectApiName())
                .layoutApiName(layoutExt.getName())
                .existMultiLanguage(existMultiLanguage)
                .componentPreKeyMap(Optional.ofNullable(layoutLogicService).map(x -> x.findComponentPreKeys(layoutExt.getComponentsSilently())).
                        orElse(newHashMap()))
                .build()
                .reset();
        //将组件排序
        Set<String> topApiNames = GroupComponentExt.of(topGroupComponent).getChildComponentsSilently().stream()
                .map(x -> x.getName()).collect(Collectors.toSet());
        List<IComponent> middleComponents = components.stream()
                .filter(x -> !topApiNames.contains(x.getName()))
                .filter(x -> !ComponentExt.of(x).isIFrameComponent() && !ComponentExt.of(x).isChartComponent())
                .collect(Collectors.toList());
        orderComponent(middleGroupComponent, middleComponents);

        //组装图表页签，用于展示图表组件和嵌入页面
        GroupComponent chartGroup = buildChartGroup(components);
        if (GroupComponentExt.of(chartGroup).containsChildComponent()) {
            middleGroupComponent.addComponent(chartGroup);
        }

        finalGroupComponentList.add(topGroupComponent);
        finalGroupComponentList.add(middleGroupComponent);

        return filterComponentByWhere(finalGroupComponentList, objectData);
    }

    private GroupComponent buildChartGroup(List<IComponent> components) {
        GroupComponent groupComponent = new GroupComponent();
        groupComponent.setName(ComponentExt.CHART_GROUP_NAME);
        String labelKey = String.format(I18NKey.CHART_GROUP, user.getTenantId(), describeExt.getApiName());
        groupComponent.setHeader(I18NExt.getOrDefault(labelKey, I18NExt.getOrDefault(I18NKey.OTHER, "其他")));// ignoreI18n

        List<IComponent> chartComponents = components.stream()
                .filter(x -> ComponentExt.of(x).isChartComponent() || ComponentExt.of(x).isIFrameComponent())
                .collect(Collectors.toList());
        chartComponents = ComponentOrder.order(chartComponents);
        groupComponent.setChildComponents(chartComponents);

        return groupComponent;
    }

    private void orderComponent(GroupComponent middleGroupComponent, List<IComponent> components) {
        ComponentOrder.order(components, layoutExt, componentConfig).forEach(middleGroupComponent::addComponent);
    }

    private List<IComponent> orderComponentsForWebDetail(IFormComponent formComponent, List<IMultiTableComponent> detailObjectComponents,
                                                         GroupComponent relatedObjectComponent, IComponent logComponent, List<RelatedObjectDescribeStructure> relatedObjectListCopy) {
        List<IComponent> components = Lists.newArrayList();
        if (formComponent != null) {
            components.add(formComponent);
        }
        components.addAll(detailObjectComponents);
        components.addAll(GroupComponentExt.of(relatedObjectComponent).getChildComponentsSilently());
        components.add(logComponent);

        if (layoutExt.isNewLayout()) {
            //自定义组件
            List<IComponent> customComponents = layoutExt.filterCustomComponents(newCustomComponents);
            components.addAll(customComponents);
            //700以后新增的组件
            List<IComponent> newComponents = getNewComponents(components);
            List<RelatedObjectDescribeStructure> allList = Lists.newArrayList(relatedObjectListCopy);
            allList.addAll(detailObjectList);
            Set<String> refObjApiNames = allList.stream()
                    .map(RelatedObjectDescribeStructure::getRelatedObjectDescribe)
                    .map(IObjectDescribe::getApiName).collect(Collectors.toSet());
            newComponents.removeIf(c -> ComponentExt.of(c).isTileRelatedList() && !refObjApiNames.contains(ComponentExt.of(c).getRefObjectApiName()));
            components.addAll(newComponents);
        } else {
            components.addAll(getBusinessComponents(layoutExt, components));
        }

        resetHeadInfoComponent(components);

        layoutExt.removeHiddenComponents(components);
        //根据i18nKey重置component的header

        boolean existMultiLanguage = licenseService.isSupportMultiLanguage(user.getTenantId());
        ComponentHeaderSetter.builder()
                .tenantId(user.getTenantId())
                .layoutType(LayoutTypes.DETAIL)
                .layoutVersion(LayoutContext.get().getLayoutVersion())
                .components(components)
                .suspendedComponents(layoutExt.getSuspendedComponent())
                .objectApiName(layoutExt.getRefObjectApiName())
                .layoutApiName(layoutExt.getName())
                .existMultiLanguage(existMultiLanguage)
                .componentPreKeyMap(Optional.ofNullable(layoutLogicService).map(x -> x.findComponentPreKeys(layoutExt.getComponentsSilently()))
                        .orElse(com.fxiaoke.functions.utils.Maps.newHashMap()))
                .build()
                .reset();
        List<IComponent> finalGroupComponentList = ComponentOrder.order(components, layoutExt, componentConfig);
        return filterComponentByWhere(finalGroupComponentList, objectData);
    }

    private void resetHeadInfoComponent(List<IComponent> components) {
        Set<String> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(user, describeExt.getApiName());
        LayoutExt.resetHeadInfoComponent(components, unauthorizedFields, describeExt, user);
    }

    private List<IComponent> getBusinessComponents(LayoutExt layoutExt, List<IComponent> components) {
        Set<String> componentApiName = components.stream().map(IComponent::getName).collect(Collectors.toSet());

        return layoutExt.getComponentsSilently().stream()
                .filter(x -> AppFrameworkConfig.isOldLayoutConvertV3AddComponents(x.getName()))
                .filter(x -> !componentApiName.contains(x.getName()))
                .collect(Collectors.toList());
    }

    private List<IComponent> filterComponentByWhere(List<IComponent> finalGroupComponentList, IObjectData objectData) {
        if (CollectionUtils.empty(finalGroupComponentList) || Objects.isNull(objectData)) {
            return finalGroupComponentList;
        }

        return finalGroupComponentList.stream()
                .map(ComponentExt::of)
                .filter(it -> it.filterComponentsByWheres(objectData, describeExt))
                .map(ComponentExt::getComponent)
                .collect(Collectors.toList());

    }

    private List<IComponent> getGroupFieldComponents(SimpleComponent simpleComponent) {
        if (CollectionUtils.empty(simpleComponent.getButtons())) {
            return Lists.newArrayList();
        }
        return simpleComponent.getButtons().stream().map(x -> convertButtonToComponent(x)).collect(Collectors.toList());
    }

    private IComponent convertButtonToComponent(IButton button) {
        GroupFieldComponent component = new GroupFieldComponent();
        component.setName(button.getAction() + "_group_field");
        component.setHeader(button.getLabel());
        component.setAction(button.getAction());
        return component;
    }

    private List<IButton> getDetailPageButtons() {
        if (excludeButton) {
            return Lists.newArrayList();
        }
        ComponentActions detailActions = getComponentActionsByApiName(describeExt.getApiName());
        List<IButton> buttonList = buttonLogicService.getButtonByComponentActions(user,
                detailActions, describeExt.getObjectDescribe(), objectData, !fromRecycleBin);

        // 特殊处理以前的按钮，与自定义按钮冲突，下发了2个
        if (describeExt.isSFAObject()) {
            buttonList.removeIf(o -> PrmConstant.PARTNER_BUTTON_NAME_SUFFIX_LIST.stream()
                    .anyMatch(x -> (describeExt.getApiName() + x).equals(o.getName())));
        }

        //锁定和作废数据移除合作伙伴相关按钮
        ObjectDataExt dataExt = ObjectDataExt.of(objectData);
        if (dataExt.isLock() || dataExt.isInvalid()) {
            buttonList.removeIf(f -> PrmConstant.PARTNER_BUTTON_ACTION_LIST.contains(f.getAction()));
        }

        //合作伙伴按钮动态变化
        dealButtonByPartner(buttonList);

        handleSystemButtonParam(buttonList);

        //添加自定义按钮
        if (!dataExt.isInvalid() && CollectionUtils.notEmpty(customButtons)) {
            Collections.reverse(customButtons);
            // 只追加自定义按钮
            customButtons.removeIf(x -> !IButton.ACTION_TYPE_CUSTOM.equals(x.getActionType()));
            // 移除UIPaaS的按钮，目前只在WBE端下发
            customButtons.removeIf(x -> !displayUIPaaSButton(x));
            buttonList.addAll(customButtons);
        }

        // 删除布局中隐藏的按钮
        List<String> hiddenButtons = layoutExt.getHiddenButtons();
        if (CollectionUtils.notEmpty(hiddenButtons)) {
            buttonList.removeIf(button -> hiddenButtons.contains(button.getName()));
        }

        //gdpr配置为不可编辑，不下发编辑按钮
        if (gdprService.validateGdprCompliance(user, ObjectAction.UPDATE.getActionCode(), describeExt.getApiName(), objectData.getId())) {
            buttonList.removeIf(x -> ObjectAction.UPDATE.getButtonApiName().equals(x.getName()));
        }

        List<IButton> buttonOrder = layoutExt.getButtonOrder();
        ButtonOrder.convertDealToTransfer(buttonOrder, describeExt.getApiName());
        //按钮排序
        return ButtonOrder.orderingByTemplate(buttonList, getButtonOrder());
    }

    private List<IButton> getButtonOrder() {
        List<IButton> buttonOrder = layoutExt.getButtonOrder();
        ButtonOrder.convertDealToTransfer(buttonOrder, describeExt.getApiName());
        if (CollectionUtils.empty(buttonOrder)) {
            //使用默认排序方案
            ButtonOrder.fillLayoutWithButtonsByDefaultOrder(layoutExt, customButtons, describeExt);
            buttonOrder = layoutExt.getButtonOrder();
        }
        // 移动端企业微信的请求,按钮排序需要将 以模板发送 和 发送附件 置顶
        if (RequestUtil.isMobileWXWorkRequest()) {
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_SEND_ATTACHMENT_BUTTON_OBJECT, describeExt.getApiName())) {
                buttonOrder.add(0, SEND_ATTACHMENT.createButton());
            }
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_SEND_AS_TEMPLATE_BUTTON_OBJECT, describeExt.getApiName())) {
                buttonOrder.add(0, SEND_AS_TEMPLATE.createButton());
            }
        }
        return buttonOrder;
    }

    private void dealButtonByPartner(List<IButton> buttonList) {
        boolean partnerIdIsEmpty = Strings.isNullOrEmpty(objectData.get(PrmConstant.FIELD_PARTNER_ID, String.class));
        // 合作伙伴为空，屏蔽『移除合作伙伴』按钮
        if (partnerIdIsEmpty) {
            buttonList.removeIf(f -> ObjectAction.DELETE_PARTNER.getActionCode().equals(f.getAction()));
        }
    }

    /**
     * 处理可以自定义按钮入参的预置按钮按钮，
     * （无效和跟进中按钮需要保留按钮入参，按钮结构需要和自定义按钮一致）
     *
     * @param buttonList
     */
    private void handleSystemButtonParam(List<IButton> buttonList) {
        List<String> leadsButtonName = buttonList.stream()
                .filter(it -> ButtonConfig.isDefaultButtonParamGray(describeExt.getApiName(), it.getName(), user.getTenantId()))
                .map(IButton::getName)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(leadsButtonName)) {
            return;
        }
        List<IButton> leadsButton = customButtons.stream()
                .filter(it -> leadsButtonName.contains(it.getName())).collect(Collectors.toList());
        buttonList.removeAll(leadsButton);
        buttonList.addAll(leadsButton);
    }

    /**
     * h5 不下发UIAction的按钮
     * 移动端请求，如果没有开启独立布局，不下发UIAction的按钮
     */
    private boolean displayUIPaaSButton(IButton button) {
        //不是UI按钮，不过滤
        if (!LayoutButtonExt.of(button).isUIPaaS()) {
            return true;
        }
        // 小程序独立于 H5
        boolean isRealH5 = !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.H5_UI_PAAS_ACTION_GRAY_EI, user.getTenantId())
                && RequestUtil.isH5MobileRequest() && !RequestUtil.isWXMiniProgram();
        if (isRealH5 || (!layoutExt.isEnableMobileLayout() && LayoutContext.isMobileLayout())) {
            return false;
        }
        return true;
    }

    private ComponentActions getComponentActionsByApiName(String apiName) {
        if (fromRecycleBin || (objectData != null && ObjectDataExt.of(objectData).isInvalid())) {
            return ComponentActions.RECYCLE_BIN_DETAIL_PAGE;
        }
        return ComponentActions.DETAIL_PAGE;
    }

    private void modifyLayoutByVersion(GroupComponent relatedObjectComponent, IComponent detailComponent,
                                       SimpleComponent simpleComponent, List<IButton> detailButtons) {
        if (LicenseConstants.Versions.VERSION_BASIC.equals(version)) {
            if (describeExt.isSFAObject()) {
                modifyDetailComponentByVersion(detailComponent, describeExt.getApiName());
                modifySimpleCompByVersion(simpleComponent, describeExt.getApiName());
                modifyButtonsByVersion(detailButtons, describeExt.getApiName());
            }
            modifyRelatedComponentByVersion(relatedObjectComponent, describeExt.getApiName());
        }
    }

    /**
     * Master details components
     *
     * @return
     */
    private List<GroupComponent> buildMasterDetailComponents() {
        if (CollectionUtils.empty(detailObjectList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.notEmpty(listLayoutMap)) {
            Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();
            detailObjectList.forEach(x -> detailDescribeMap.put(x.getRelatedObjectDescribe().getApiName(), x.getRelatedObjectDescribe()));
            listLayoutMap.forEach((x, y) -> TableComponentRender.builder()
                    .functionPrivilegeService(functionPrivilegeService)
                    .user(user)
                    .describeExt(ObjectDescribeExt.of(detailDescribeMap.get(x)))
                    .tableComponentExt(TableComponentExt.of(LayoutExt.of(y).getTableComponent().get()))
                    .build()
                    .render());
        }

        return MasterDetailGroupComponentBuilder.builder()
                .user(user)
                .detailObjectsDescribeList(detailObjectList)
                .objectDescribeExt(describeExt)
                .objectData(objectData)
                .layoutExt(layoutExt)
                .listLayoutMap(listLayoutMap)
                .recordTypeOptionMap(recordTypeOptionMap)
                .buttonLogicService(buttonLogicService)
                .changeOrderLogicService(changeOrderLogicService)
                .build()
                .getComponentList();
    }

    private List<IMultiTableComponent> buildNewMasterDetailComponents() {
        if (CollectionUtils.empty(detailObjectList)) {
            return Lists.newArrayList();
        }

        return MasterDetailGroupComponentBuilder.builder()
                .user(user)
                .detailObjectsDescribeList(detailObjectList)
                .objectDescribeExt(describeExt)
                .objectData(objectData)
                .layoutExt(layoutExt)
                .changeOrderLogicService(changeOrderLogicService)
                .build()
                .getMultiTableList();
    }

    /**
     * 创建管理对象Component
     *
     * @return
     */
    private GroupComponent buildRelatedComponent() {
        layoutLogicService.filterRelatedObjectStructure(user, layoutExt, relatedObjectList);
        GroupComponent groupComponent = RelatedObjectGroupComponentBuilder.builder()
                .user(user)
                .pageType(pageType)
                .objectDescribe(describeExt)
                .objectData(objectData)
                .relatedObjectDescribeList(relatedObjectList)
                .layout(layoutExt)
                .buttonLogicService(buttonLogicService)
                .functionPrivilegeService(functionPrivilegeService)
                .changeOrderLogicService(changeOrderLogicService)
                .build()
                .getGroupComponent();
        List<IComponent> components = GroupComponentExt.of(groupComponent).getChildComponentsSilently();
        //使用新版附件替换旧版附件
        LayoutComponents.replaceOldAttachComponent(layoutExt, components);
        groupComponent.setChildComponents(components);
        return groupComponent;
    }

    private void modifyButtonsByVersion(List<IButton> allButtons, String apiName) {
        List<String> invisibleActions = BasicVersionInvisibleMapping.getInvisibleActions(apiName);
        if (CollectionUtils.empty(invisibleActions)) {
            return;
        }
        allButtons.removeIf(x -> invisibleActions.contains(x.getAction()));
    }

    private void modifyDetailComponentByVersion(IComponent detailComponent, String apiName) {
        if (detailComponent == null) {
            return;
        }
        List<String> invisibleFields = BasicVersionInvisibleMapping.getInvisibleFields(apiName);
        if (CollectionUtils.empty(invisibleFields)) {
            return;
        }
        if (detailComponent instanceof GroupComponent) {
            List<Map> childComponents = (List<Map>) detailComponent.get("child_components");
            for (Map childComponent : childComponents) {
                List<Map> fieldSections = (List<Map>) childComponent.get("field_section");
                removeInvisibleFields(fieldSections, invisibleFields);
            }
        } else {
            List<Map> fieldSections = (List<Map>) detailComponent.get("field_section");
            removeInvisibleFields(fieldSections, invisibleFields);
        }
    }

    private void modifyRelatedComponentByVersion(GroupComponent relatedComponent, String apiName) {
        List<String> invisibleRefObjs = BasicVersionInvisibleMapping.getInvisibleRefObjects(apiName);
        List<Map> relatedDocList = (List<Map>) relatedComponent.getContainerDocument().get("child_components");
        relatedDocList.removeIf(x -> {
            Object refObjApiNameObj = x.get("ref_object_api_name");
            if (refObjApiNameObj == null) {
                return false;
            }
            String refObjApiName = refObjApiNameObj.toString();
            //A版不支持关联自定义对象
            return (refObjApiName.endsWith("__c") ||
                    (invisibleRefObjs != null && invisibleRefObjs.contains(refObjApiName)));
        });
    }

    private void modifySimpleCompByVersion(SimpleComponent simpleComponent, String apiName) {
        List<String> invisibleFields = BasicVersionInvisibleMapping.getInvisibleFields(apiName);
        if (CollectionUtils.empty(invisibleFields)) {
            return;
        }
        List<Map> fieldSections = (List<Map>) simpleComponent.getContainerDocument().get("field_section");
        removeInvisibleFields(fieldSections, invisibleFields);
    }

    private void removeInvisibleFields(List<Map> fieldSections, List<String> invisibleFields) {
        for (Map section : fieldSections) {
            List<Map> formFields = (List<Map>) section.get("form_fields");
            formFields.removeIf(x -> invisibleFields.contains(x.get("field_name").toString()));
        }
    }

    private GroupComponent getDetailGroupComponent() {
        GroupComponent detailInfo = new GroupComponent();
        detailInfo.setName("detailInfo");
        detailInfo.setHeader(I18N.text(I18NKey.DETAIL_INFO));

        IFormComponent formComponent = getDetailFormComponent();
        if (Objects.isNull(formComponent)) {
            return detailInfo;
        }
        formComponent.setHeader(I18N.text(I18NKey.DETAIL_INFO));
        detailInfo.addComponent(formComponent);

        return detailInfo;
    }

    private IFormComponent getDetailFormComponent() {
        Optional<FormComponentExt> formComponent = layoutExt.getFormComponent();
        if (!formComponent.isPresent()) {
            return null;
        }
        layoutExt.fillFormComponent();
        return formComponent.get().getFormComponent();
    }

    private GroupComponent getLogGroupComponent() {
        GroupComponent result = new GroupComponent();
        result.setName("otherInfo");
        result.setHeader(I18N.text(I18NKey.OTHER_INFO));

        //布局设计器设置隐藏的话就不下发了
        if (layoutExt.isComponentHidden(ComponentExt.OPERATION_LOG_COMPONENT_NAME)) {
            return result;
        }

        if (layoutExt.isNewLayout() && !layoutExt.containsComponent(ComponentExt.OPERATION_LOG_COMPONENT_NAME)) {
            return result;
        }

        //修改记录component
        IRelatedRecordComponent modifyRecordComponent = LayoutExt.buildModifyRecordComponent();
        setRecordComponentShowDateRange(modifyRecordComponent);
        List<IComponent> componentList = Lists.newArrayList();
        componentList.add(modifyRecordComponent);

        // 670之前的版本在其他信息中显示从对象修改记录，670之后的版本不显示
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_670)) {
            for (RelatedObjectDescribeStructure detailObject : detailObjectList) {
                IObjectDescribe detailDescribe = detailObject.getRelatedObjectDescribe();
                IRelatedRecordComponent detailModifyRecordComponent = LayoutExt.buildModifyRecordComponent();
                detailModifyRecordComponent.setName("operation_log" + "_" + detailDescribe.getApiName());
                detailModifyRecordComponent.setHeader(detailDescribe.getDisplayName());
                detailModifyRecordComponent.set("detailApiName", detailDescribe.getApiName());
                if (!detailModifyRecordComponent.isHidden()) {
                    componentList.add(detailModifyRecordComponent);
                }
            }
        }

        result.setOrder(modifyRecordComponent.getOrder());
        result.setChildComponents(componentList);

        return result;
    }

    private RelatedRecordComponent getLogComponent() {
        RelatedRecordComponent result = new RelatedRecordComponent();
        result.setName(ComponentExt.OPERATION_LOG_COMPONENT_NAME);
        result.setHeader(I18N.text(I18NKey.MODIFY_LOG));
        setRecordComponentShowDateRange(result);
        return result;
    }

    private void setRecordComponentShowDateRange(IRelatedRecordComponent result) {
        result.set("show_date_range", AuditLogConfig.isGrayAuditLogChRead(user.getTenantId()));
    }

    private SimpleComponent getSimpleComponent() {
        return TopInfoComponentBuilder.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .user(user)
                .describeExt(describeExt)
                .layoutExt(layoutExt)
                .objectData(objectData)
                .build()
                .getSimpleComponent();
    }

    private void handleRecordTypeMatchRelation(List<GroupComponent> detailObjectComponents) {
        if (CollectionUtils.empty(detailObjectComponents) || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_673)) {
            return;
        }
        List<String> detailApiNameList = detailObjectList.stream()
                .map(x -> x.getRelatedObjectDescribe().getApiName())
                .collect(Collectors.toList());
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = recordTypeLogicService.findValidRecordTypeListMap(detailApiNameList, user);
        validRecordTypeListMap = recordTypeLogicService.filterUnMatchRecordTypes(user.getTenantId(), validRecordTypeListMap, objectData.getDescribeApiName(), objectData.getRecordType());

        try {
            for (GroupComponent detailComponent : detailObjectComponents) {
                if (detailComponent != null && CollectionUtils.notEmpty(detailComponent.getChildComponents())) {
                    String detailApiName = (String) detailComponent.getChildComponents().get(0).get("ref_object_api_name");
                    List<String> validRecordType = validRecordTypeListMap.getOrDefault(detailApiName, Lists.newArrayList()).stream().map(x -> x.getApiName()).collect(Collectors.toList());
                    for (IComponent component : detailComponent.getChildComponents()) {
                        IMultiTableComponent tableComponent = (IMultiTableComponent) component;
                        List<ITableComponent> childComponents = tableComponent.getChildComponents();
                        childComponents.forEach(x -> {
                            if (validRecordType.contains(x.getRefObjectApiName())) {
                                x.set("not_match", false);
                            } else {
                                x.set("not_match", true);
                            }
                        });
                    }
                }
            }
        } catch (MetadataServiceException e) {
            log.error("handleRecordTypeMatchRelation error ", e);
        }
    }

}