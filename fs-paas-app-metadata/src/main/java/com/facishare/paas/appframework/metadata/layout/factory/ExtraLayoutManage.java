package com.facishare.paas.appframework.metadata.layout.factory;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ExtraLayoutManage implements ApplicationContextAware {
    private static final Map<String, ExtraLayoutFactory> layoutProcessorFactoryMap = new ConcurrentHashMap<>();


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ExtraLayoutFactory> map = applicationContext.getBeansOfType(ExtraLayoutFactory.class);
        map.forEach((key, value) -> layoutProcessorFactoryMap.put(value.getLayoutType(), value));
    }


    public ExtraLayoutFactory getLayoutFactory(String layoutType) {
        return layoutProcessorFactoryMap.get(layoutType);
    }

}
