package com.facishare.paas.appframework.metadata.layout.factory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ExtraLayoutManage {
    private static final Map<String, ExtraLayoutFactory> layoutProcessorFactoryMap = new ConcurrentHashMap<>();


    public static void register(String layoutType, ExtraLayoutFactory factory) {
        layoutProcessorFactoryMap.put(layoutType, factory);
    }


    public static ExtraLayoutFactory getLayoutFactory(String layoutType) {
        return layoutProcessorFactoryMap.get(layoutType);
    }

}
